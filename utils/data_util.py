import json

from loguru import logger
from utils.ck_util import get_ck_data
from const.default_data import VTSettingData, DefaultSwitchEPData
import time

def get_vt_last_actual_data(vt_expected_data_object):
    """
    查询上一条埋点数据
    """
    # 初始化查询数据
    log_id = VTSettingData.log_id
    buvid = VTSettingData.get_buvid()
    event_id = ""
    expected_size = 1
    ck_from_time = int(time.time()) - 5 * 60
    logger.info(f"获取埋点数据, buvid: {buvid}, log_id: {log_id}, event_id: {event_id}, expected_size: {expected_size}, ck_from_time: {ck_from_time}")

    # 获取实际数据
    data, actual_size, message = get_ck_data(
        log_id, buvid, event_id, expected_size, ck_from_time,
        *list(vt_expected_data_object.get_vt_expect_data().keys())
    )

    # 检查是否获取到数据
    if not data or log_id not in data or not data[log_id]:
        raise ValueError(f"未获取到有效的埋点数据: {message}")

    return data[log_id][-1]


def get_vt_switch_ep_actual_data(vt_expected_data_object):
    # 初始化查询数据, 可能会重复上报, 所以捞多条数据
    log_id = VTSettingData.log_id
    buvid = VTSettingData.buvid
    event_id = ""
    expected_size = 5
    ck_from_time = int(time.time()) - 5 * 60

    # 获取实际数据
    data, actual_size, message = get_ck_data(
        log_id, buvid, event_id, expected_size, ck_from_time,
        *list(vt_expected_data_object.get_vt_expect_data().keys())
    )
    logger.info(f"获取到的埋点数据: {json.dumps(data, ensure_ascii=False)}")

    # 只需要两条数据, 一条任意时间参数不为 0, 一条时间参数为 0
    actual_data = {}
    for item in data[log_id]:
        if item['played_time'] != '0':
            actual_data['play_end_data'] = item
            break
    for item in data[log_id]:
        if item['played_time'] == '0':
            actual_data['play_start_data'] = item
            break

    if len(actual_data) != 2:
        logger.error(f"获取到的埋点数据不满足断言条件, 实际数据: {actual_data}")
        raise Exception(f"获取到的埋点数据不满足断言条件")

    logger.info(f"切换 ep 校验实际数据: {json.dumps(actual_data, ensure_ascii=False)}")
    return actual_data


def get_vt_switch_ep_expect_data(vt_expect_data_object, from_spmid=DefaultSwitchEPData.from_spmid,
                                 aid=DefaultSwitchEPData.aid, cid=DefaultSwitchEPData.cid, epid=DefaultSwitchEPData.epid):
    import copy

    expect_data = {}

    # 先保存当前状态的深拷贝作为播放结束数据
    expect_data['play_end_data'] = copy.deepcopy(vt_expect_data_object.get_vt_expect_data())

    # 保存原始数据状态，以便后续恢复
    original_data = copy.deepcopy(vt_expect_data_object.data)

    # 更新播放开始的期望数据
    vt_expect_data_object.update_expect_play_time_data(
        played_time=0,
        paused_time=0,
        total_time=0,
        actual_played_time=0,
        last_play_progress_time=0,
        max_play_progress_time=0
    )
    vt_expect_data_object.update_vt_expect_data(
        from_spmid=from_spmid,
        aid=aid,
        cid=cid,
        epid=epid
    )

    # 获取播放开始数据
    expect_data['play_start_data'] = copy.deepcopy(vt_expect_data_object.get_vt_expect_data())

    # 恢复原始数据状态，避免影响后续操作
    vt_expect_data_object.data = original_data

    logger.info(f"切换 ep 校验期望数据: {json.dumps(expect_data, ensure_ascii=False)}")
    return expect_data