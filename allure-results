Given mock规则"{mock_rule}"

Given 打开app

Given 进入追番tab

Given 登录用户, 用户名{username}, 密码{password}

Given 测试一下

When 等待{seconds}秒

When AI执行用例, case-id: {case_id}

When 截图"{name}"

When 点击天马卡

When 点击"{target_text}"文字,匹配图片"{image}"

When 点击退出按钮

When 设置进入详情页暂停

When 点击"{img1_name}",匹配图片"{img2_name}"

When 下滑至页面底部

When seek至视频进度{seconds:d}秒

When 设置进入详情页暂停{}秒

When 设置实际播放时间{seconds}秒

When 获取进度条时间

When 设置小窗播放时长{seconds}秒

When 设置播放清晰度"{quality}"

When 测试when

When 播放{seconds}秒

When 暂停{seconds}秒

When seek至{seconds}秒

Then 播放开始时上报断言

Then 播放结束时上报断言

Then 获取期待的vt data

Then 断言vt的api校验


