from time import sleep

import allure
from behave import given, when, then

from android.android_util.android_page_player import AndroidPagePlayer
from const.Data import IOS
from utils.assert_util import _check_vt_play_start_data
from utils.old_utils.expect_data_util import get_vt_expect_data


@given('测试一下')
def step_impl(context):
    if context.platform == 'ios':
        print("啦啦啦啦啦啦啦啦ios测试")
    if context.platform == 'android':
        print("啦啦啦啦啦啦啦啦android测试")


@when('测试when')
def step_impl(context):
    if context.platform == 'ios':
        print("啦啦啦啦啦啦啦啦when测试")
    if context.platform == 'android':
        print("啦啦啦啦啦啦啦啦android when测试")


@when("播放{seconds}秒")
def step_impl(context, seconds):
    sleep(int(seconds))
    context.played_time = int(seconds)


@when("暂停{seconds}秒")
def step_impl(context, seconds):
    sleep(int(seconds))
    context.paused_time = int(seconds)


@when("seek至{seconds}秒")
def step_impl(context, seconds):
    total_seconds = AndroidPagePlayer.seek_to_time(context.platform)
    context.seeked_time = total_seconds


@then('获取期待的vt data')
def step_impl(context):
    print("获取期待的vt data")

    expect_data = get_vt_expect_data("android", None, "7838967145753480", IOS.buvid, context.played_time,
                                     context.played_time, context.played_time + context.paused_time,
                                     context.played_time, context.played_time, context.played_time, 0,
                                     "32",
                                     "tm.recommend.0.0", "united.player-video-detail.0.0","united.player-video-detail.0.0")
    context.expect_data = expect_data
    print(context.expect_data)
    allure.attach(str(expect_data), name="Expected Value", attachment_type=allure.attachment_type.JSON)


@then('断言vt的api校验')
def step_impl(context):
    print("断言vt的api校验")
    context.actual_api_values = {
        'cess_key': '3748526b07a3f1de410b6018518b96c1CjCOPLGSEeSYQPbPrxYggyIgh28FWI5WYpEKXW-2imKJt-fgzuZkY1kHnIJT1QmKEUoSVjVmbUxPaWg1VW43bE9DTVFQRHdJOGpZd0dOQ0VXQ2N0TGtpZFRrZHhZNVN2ckZCNGNGRXdGTl81M0luQW5CVUQ3TEJNcGtpZ0E2UW5lLXMyTVRlWVd3IIEC',
        'actual_played_time': '11', 'aid': '680122939', 'appkey': '1d8b6e7d45233436', 'auto_play': '2',
        'build': '8100000', 'c_locale': 'zh_CN', 'channel': 'vivo', 'cid': '10258390', 'disable_rcmd': '0',
        'epid': '137187', 'epid_status': '2', 'extra': '%7B%22from_outer_spmid%22%3A%22tm.recommend.0.0%22%7D',
        'from': '76', 'from_spmid': 'tm.recommend.0.0', 'last_play_progress_time': '9', 'list_play_time': '11',
        'max_play_progress_time': '9', 'mid': '7838967145753480', 'miniplayer_play_time': '0', 'mobi_app': 'android',
        'network_type': '1', 'oaid': '', 'paused_time': '0', 'platform': 'android', 'play_status': '1',
        'play_type': '2', 'played_time': '11', 'polaris_action_id': '498B14BC', 'quality': '32', 's_locale': 'zh_CN',
        'session': '5b92cec1f7311a7d00ee9ea5cb8f49c897c2a180', 'sid': '35914',
        'spmid': 'united.player-video-detail.0.0',
        'start_ts': '1733231417',
        'statistics': '%7B%22appId%22%3A1%2C%22platform%22%3A3%2C%22version%22%3A%228.10.0%22%2C%22abtest%22%3A%22%22%7D',
        'sub_type': '1', 'total_time': '11', 'track_id': '', 'ts': '1733231431', 'type': '4', 'user_status': '1',
        'video_duration': '215', 'sign': '296751cfc4f8c44c6cd29dabf735071',
        'buvid': 'YF456DF5968C2D6B42D0806F445C1C3E090A'}
    api_result, mismatches = _check_vt_play_start_data(context.expect_data, context.actual_api_values)
    allure.attach(str(context.actual_api_values), name="Actual API Values", attachment_type=allure.attachment_type.JSON)
    assert api_result == True, f"断言vt的api校验失败: {mismatches}"
