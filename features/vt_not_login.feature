@allure.feature:未登录
Feature: # 未登录

  Scenario: # 无历史记录, 进入半屏视频详情页, 播放视频, 退出视频详情页
    Given mock规则: ogvfeed天马首页
    Given 更新埋点数据: {"quality": 32}
    Given 打开app
    When AI执行用例步骤, desc: 进入视频详情页
    Then 播放开始时上报断言
    When AI执行用例步骤, desc: 退出视频详情页
    Then 播放结束时上报断言

  Scenario: # 进入视频详情页, 倍速播放
    Given mock规则: ogvfeed天马首页
    Given 更新埋点数据: {"quality": 32}
    Given 打开app
    When AI执行用例步骤, desc: 进入视频详情页, 切换倍速
    Then 播放开始时上报断言
    When AI执行用例步骤, desc: 退出视频详情页
    Then 播放结束时上报断言

  Scenario: # 进入视频详情页, 倍速 + 切ep
    Given mock规则: ogvfeed天马首页
    Given 更新埋点数据: {"quality": 32}
    Given 打开app
    When AI执行用例步骤, desc: 进入视频详情页
    Then 播放开始时上报断言
    When AI执行用例步骤, desc: 在视频详情页切换ep
    Then 切换ep上报断言
    When AI执行用例步骤, desc: 退出视频详情页
    Then 播放结束时上报断言
